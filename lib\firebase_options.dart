// Generated manually based on existing GoogleService-Info.plist and google-services.json
// Provides explicit FirebaseOptions to make initialization deterministic on iOS Release.
// If you later run `flutterfire configure`, it can overwrite this file with the
// auto-generated version. Values are taken from:
// - ios/Runner/GoogleService-Info.plist
// - android/app/google-services.json
// - public/admin-*.html (for web)

import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart' show defaultTargetPlatform, kIsWeb, TargetPlatform;

class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        // macOS는 사용하지 않으므로 iOS 값 재사용 또는 별도 구성 필요
        return ios;
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions는 현재 플랫폼을 지원하지 않습니다: '
          '$defaultTargetPlatform',
        );
    }
  }

  // Web (from public/admin-login.html)
  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCqCc7aTMTXAGJfpb7fYe713EuTbGKEzMI',
    appId: '1:699872938105:web:c4c31cde360147caf3aca8',
    messagingSenderId: '699872938105',
    projectId: 'parabara-1a504',
    authDomain: 'parabara-1a504.firebaseapp.com',
    storageBucket: 'parabara-1a504.firebasestorage.app',
  );

  // Android (from android/app/google-services.json)
  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAbs9Ai6rQFGsvmGkPvdaWXHSZq2y4c4Yc',
    appId: '1:699872938105:android:64a0052ecfc10797f3aca8',
    messagingSenderId: '699872938105',
    projectId: 'parabara-1a504',
    storageBucket: 'parabara-1a504.firebasestorage.app',
  );

  // iOS (from ios/Runner/GoogleService-Info.plist)
  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyC_9ghE0y1N7JDzPe6ojfRspfLUq6fNQsw',
    appId: '1:699872938105:ios:a2ed9c4fde333368f3aca8',
    messagingSenderId: '699872938105',
    projectId: 'parabara-1a504',
    storageBucket: 'parabara-1a504.firebasestorage.app',
    iosBundleId: 'com.blue.parabara',
    iosClientId: '699872938105-kglrtk2qp1dfs455k8pjp577n4ir6h2a.apps.googleusercontent.com',
  );
}

