import Flutter
import UIKit
import Firebase
import GoogleSignIn

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    // Firebase 초기화는 Flutter(Dart)에서 Firebase.initializeApp()으로 수행합니다.
    // iOS 네이티브에서 중복 초기화를 피하기 위해 configure()는 호출하지 않습니다.
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  // Google Sign In 및 기타 URL Scheme 처리
  override func application(
    _ app: UIApplication,
    open url: URL,
    options: [UIApplication.OpenURLOptionsKey : Any] = [:]
  ) -> <PERSON>ol {
    // Google Sign In URL 처리
    if GIDSignIn.sharedInstance.handle(url) {
      return true
    }

    // 기타 URL Scheme 처리 (Apple Sign In 등)
    return super.application(app, open: url, options: options)
  }
}
