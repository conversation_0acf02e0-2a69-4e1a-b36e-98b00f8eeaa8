
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>바라 부스 매니저-동인 부스 관리앱</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>바라 부스 매니저</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LSMinimumSystemVersion</key>
	<string>15.0</string>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	
	<!-- Camera Permission -->
	<key>NSCameraUsageDescription</key>
	<string>상품 사진 촬영 및 QR코드 스캔을 위해 카메라 접근이 필요합니다.</string>
	
	<!-- Photo Library Permission -->
	<key>NSPhotoLibraryUsageDescription</key>
	<string>상품 이미지 선택을 위해 사진 라이브러리 접근이 필요합니다.</string>

	<!-- URL Schemes for Firebase Auth -->
	<key>CFBundleURLTypes</key>
	<array>
		<!-- Google Sign In URL Scheme -->
		<dict>
			<key>CFBundleURLName</key>
			<string></string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.699872938105-kglrtk2qp1dfs455k8pjp577n4ir6h2a</string>
			</array>
		</dict>
		<!-- Apple Sign In URL Scheme -->
		<dict>
			<key>CFBundleURLName</key>
			<string>com.blue.parabara.apple</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.blue.parabara</string>
			</array>
		</dict>
	</array>

	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
</dict>
</plist>
