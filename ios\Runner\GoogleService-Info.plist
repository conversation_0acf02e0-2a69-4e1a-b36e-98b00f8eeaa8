<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>699872938105-kglrtk2qp1dfs455k8pjp577n4ir6h2a.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.699872938105-kglrtk2qp1dfs455k8pjp577n4ir6h2a</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>699872938105-3nrghoifs1inem7m41dvup8kftuk268j.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyC_9ghE0y1N7JDzPe6ojfRspfLUq6fNQsw</string>
	<key>GCM_SENDER_ID</key>
	<string>699872938105</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.blue.parabara</string>
	<key>PROJECT_ID</key>
	<string>parabara-1a504</string>
	<key>STORAGE_BUCKET</key>
	<string>parabara-1a504.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:699872938105:ios:a2ed9c4fde333368f3aca8</string>
</dict>
</plist>