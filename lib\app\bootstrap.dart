import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'dart:io' show Platform;
import '../utils/image_cache.dart';
import '../utils/logger_utils.dart';
import '../utils/mobile_performance_utils.dart';
import '../utils/app_check_config.dart';
import '../utils/app_version_utils.dart';
import '../services/remote_config_service.dart';
import 'package:intl/date_symbol_data_local.dart';
import '../firebase_options.dart';

/// 스플래시 스크린 이미지 미리 로드 (rootBundle 사용)
Future<void> _precacheSplashImages() async {
  try {
    // rootBundle을 사용하여 이미지를 메모리에 미리 로드
    await rootBundle.load('assets/images/bara_icon.png');
    LoggerUtils.logInfo('스플래시 이미지 미리 로드 완료', tag: 'Bootstrap');
  } catch (e) {
    // 로드 실패 시 무시 (fallback으로 'B' 텍스트 사용)
    LoggerUtils.logWarning('스플래시 이미지 미리 로드 실패: $e', tag: 'Bootstrap');
  }
}

/// 공통 초기화 (플러터 바인딩, 시스템 UI, Firebase 등)
Future<void> bootstrapApplication() async {
  WidgetsFlutterBinding.ensureInitialized();

  SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
    statusBarColor: Colors.transparent,
    statusBarBrightness: Brightness.light,
    statusBarIconBrightness: Brightness.dark,
    systemNavigationBarColor: Colors.white,
    systemNavigationBarDividerColor: Colors.transparent,
    systemNavigationBarIconBrightness: Brightness.dark,
  ));

  SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge, overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom]);

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight,
  ]);

  ImageCacheManager.initialize();
  ImageCacheManager.schedulePeriodicOptimization();

  // 스플래시 스크린 이미지 미리 캐시 (성능 최적화)
  await _precacheSplashImages();

  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    LoggerUtils.logInfo('Firebase 초기화 완료 (기본 앱)', tag: 'Bootstrap');

    // Firebase Auth 지속성 설정 (웹 전용 - 모바일에서는 건너뛰기)
    try {
      if (kIsWeb) {
        await FirebaseAuth.instance.setPersistence(Persistence.LOCAL);
        LoggerUtils.logInfo('Firebase Auth 지속성 설정 완료 (웹)', tag: 'Bootstrap');
      } else {
        LoggerUtils.logInfo('Firebase Auth 지속성 설정 건너뜀 (모바일)', tag: 'Bootstrap');
      }
    } catch (e) {
      LoggerUtils.logError('Firebase Auth 지속성 설정 실패', tag: 'Bootstrap', error: e);
    }

    // Firebase App Check 초기화 (프로덕션 준비)
    try {
      await _initializeAppCheck();
      LoggerUtils.logInfo('Firebase App Check 초기화 완료', tag: 'Bootstrap');
    } catch (e) {
      LoggerUtils.logError('Firebase App Check 초기화 실패', tag: 'Bootstrap', error: e);
      // App Check 실패해도 앱은 계속 실행 (개발 환경 호환성)
    }

    // Firebase Analytics 로그 최적화 (개발 중에는 비활성화)
    try {
      await FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(false);
      LoggerUtils.logInfo('Firebase Analytics 비활성화 완료', tag: 'Bootstrap');
    } catch (e) {
      LoggerUtils.logError('Firebase Analytics 설정 실패', tag: 'Bootstrap', error: e);
    }
  } catch (e) {
    LoggerUtils.logError('Firebase 초기화 실패', tag: 'Bootstrap', error: e);
    // Firebase 초기화 실패해도 앱은 계속 실행
  }

  await initializeDateFormatting('ko_KR', null);

  // 앱 버전 정보 초기화
  await AppVersionUtils.initialize();
  AppVersionUtils.printVersionInfo();

  // Remote Config 초기화 (앱 버전 체크용)
  await RemoteConfigService.initialize();

  // Mobile Scanner 초기화는 첫 사용 시 자동으로 처리됨
  LoggerUtils.logInfo('Mobile Scanner는 첫 사용 시 자동 초기화됩니다', tag: 'Bootstrap');

  // 성능 최적화 초기화 (레거시 main.dart 흐름 복원)
  MobilePerformanceUtils.initializeOptimizations();
}

/// Firebase App Check 초기화
/// 디버그 모드에서는 디버그 프로바이더 사용, 릴리즈 모드에서는 프로덕션 프로바이더 사용
Future<void> _initializeAppCheck() async {
  try {
    if (!AppCheckConfig.isAppCheckEnabled) {
      LoggerUtils.logInfo('⚠️ App Check가 비활성화되어 있습니다', tag: 'AppCheck');
      return;
    }

    LoggerUtils.logInfo('App Check 초기화 시작...', tag: 'AppCheck');

    try {
      await FirebaseAppCheck.instance.activate(
        // Android: 디버그 모드에서는 debug, 릴리즈 모드에서는 playIntegrity
        androidProvider: AppCheckConfig.androidProvider,
        // iOS: 디버그 모드에서는 debug, 릴리즈 모드에서는 appAttest
        appleProvider: AppCheckConfig.appleProvider,
      );

      LoggerUtils.logInfo(
        'App Check 활성화 완료 - Android: ${AppCheckConfig.androidProvider}, iOS: ${AppCheckConfig.appleProvider}',
        tag: 'AppCheck'
      );
    } catch (e) {
      // iOS에서 App Attest 미지원/실패 시 DeviceCheck로 폴백 시도
      final isIOS = !kIsWeb && Platform.isIOS;
      final usingAppAttest = AppCheckConfig.appleProvider == AppleProvider.appAttest;
      LoggerUtils.logWarning('App Check 1차 활성화 실패: $e', tag: 'AppCheck');
      if (isIOS && usingAppAttest) {
        try {
          await FirebaseAppCheck.instance.activate(
            androidProvider: AppCheckConfig.androidProvider,
            appleProvider: AppleProvider.deviceCheck,
          );
          LoggerUtils.logInfo('iOS App Attest 실패로 DeviceCheck로 폴백 성공', tag: 'AppCheck');
        } catch (e2) {
          LoggerUtils.logError('App Check 폴백(DeviceCheck)도 실패: $e2', tag: 'AppCheck');
          rethrow;
        }
      } else {
        rethrow;
      }
    }

    // 디버그 모드에서는 토큰 정보 로그 출력
    if (AppCheckConfig.isDebugMode) {
      LoggerUtils.logInfo('🔧 디버그 모드: App Check 디버그 프로바이더 사용 중', tag: 'AppCheck');

      // 디버그 토큰 생성 및 출력
      try {
        final token = await FirebaseAppCheck.instance.getToken();
        if (token != null) {
          final tokenPreview = token.length > 50 ? token.substring(0, 50) : token;
          LoggerUtils.logInfo(
            '🔑 디버그 App Check 토큰 생성됨 (길이: ${token.length}, 미리보기: $tokenPreview...)',
            tag: 'AppCheck'
          );
          print('🔑 DEBUG APP CHECK TOKEN: $token');
          print('📋 Firebase Console > App Check > Apps에서 이 토큰을 등록하세요!');
          print('📋 등록 방법: 해당 앱 선택 > 디버그 토큰 추가');
        } else {
          LoggerUtils.logError('App Check 토큰이 null입니다', tag: 'AppCheck');
        }
      } catch (e) {
        LoggerUtils.logError('App Check 토큰 생성 실패', tag: 'AppCheck', error: e);
        print('❌ App Check 토큰 생성 실패: $e');
      }
    } else {
      LoggerUtils.logInfo('🔒 프로덕션 모드: App Check 프로덕션 프로바이더 사용 중', tag: 'AppCheck');
    }

  } catch (e) {
    LoggerUtils.logError('App Check 초기화 실패', tag: 'AppCheck', error: e);
    rethrow;
  }
}
