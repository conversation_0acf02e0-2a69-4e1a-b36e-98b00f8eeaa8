import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:go_router/go_router.dart';
import 'firebase_options.dart';
import 'pages/admin/admin_login_page.dart';
import 'pages/admin/admin_dashboard_page.dart';
import 'services/admin_service.dart';

/// 관리자 전용 앱 엔트리 포인트
Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
  } catch (e) {
    print('Firebase 초기화 오류: $e');
  }

  runApp(
    const ProviderScope(
      child: AdminApp(),
    ),
  );
}

/// 관리자 라우터 설정
final GoRouter _router = GoRouter(
  initialLocation: '/',
  routes: [
    GoRoute(
      path: '/',
      builder: (context, state) => const AdminLoginPage(),
    ),
    GoRoute(
      path: '/dashboard',
      builder: (context, state) => const AdminDashboardPage(),
      redirect: (context, state) async {
        final token = await AdminService.getToken();
        if (token == null) {
          return '/';
        }
        return null;
      },
    ),
  ],
);

/// 관리자 전용 앱
class AdminApp extends StatelessWidget {
  const AdminApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: 'Parabara Admin',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primarySwatch: Colors.grey,
        fontFamily: 'Pretendard',
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF495057),
          brightness: Brightness.light,
        ),
      ),
      routerConfig: _router,
    );
  }
}
